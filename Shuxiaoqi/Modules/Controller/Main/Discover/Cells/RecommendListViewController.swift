//
//  RecommendListViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/5/16.
//

import UIKit
import JXSegmentedView
import MJRefresh

// 添加代理协议
protocol RecommendListViewControllerDelegate: AnyObject {
    func recommendListViewController(_ controller: RecommendListViewController, didSelectProduct product: RecommendedGoodItem)
    func recommendListViewController(_ controller: RecommendListViewController, didSelectVideo video: VideoItem, inGroup group: VideoGroup)
}

// 推荐列表控制器（重构为CollectionView实现）
class RecommendListViewController: BaseViewController, UICollectionViewDelegate, UICollectionViewDataSource {
    // 添加代理属性
    weak var delegate: RecommendListViewControllerDelegate?
    
    // 视频分组数据
    private var videoGroups: [VideoGroup] = []
    // 商品数据
    private var productItems: [RecommendedGoodItem] = []
    private var currentPage: Int = 0
    private var totalPage: Int = 1
    private var isLoadingData: Bool = false
    
    private var collectionView: UICollectionView!
    
    override func viewDidLoad() {
        super.viewDidLoad()
        isTabBarRootViewController = false
        showNavBar = false
        view.backgroundColor = .appBackgroundGray
        setupCollectionView()
        setupLoadMoreFooter()
        collectionView.contentInsetAdjustmentBehavior = .never

        print("collectionView.contentInset:", collectionView.contentInset)
        collectionView.contentInset.bottom = 0
        collectionView.scrollIndicatorInsets.bottom = 0

        // 添加MJRefresh下拉刷新
        collectionView.mj_header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(handlePullToRefresh))

        // 注意：不在 viewDidLoad 中直接加载数据，而是等待 listDidAppear 回调
        // 这样可以确保只有当页面真正显示时才加载数据，避免不必要的网络请求
        print("RecommendListViewController: viewDidLoad 完成，等待 listDidAppear 触发数据加载")
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        collectionView.contentInset = .zero
        collectionView.scrollIndicatorInsets = .zero
    }
    
    private func setupCollectionView() {
        let layout = UICollectionViewCompositionalLayout { [weak self] section, env in
            guard let self = self else { return nil }
            if section < self.videoGroups.count {
                // 视频分组section：header+1个横滑cell
                let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1.0), heightDimension: .absolute(391))
                let item = NSCollectionLayoutItem(layoutSize: itemSize)
                let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1.0), heightDimension: .absolute(391))
                let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitems: [item])
                let sectionLayout = NSCollectionLayoutSection(group: group)
                sectionLayout.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0)
                // Header
                let headerSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1.0), heightDimension: .absolute(36))
                let header = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: headerSize, elementKind: UICollectionView.elementKindSectionHeader, alignment: .top)
                sectionLayout.boundarySupplementaryItems = [header]
                // 让补充视图（Header/Footer）不受 section contentInsets 的影响，iOS 14+
                if #available(iOS 14.0, *) {
                    sectionLayout.supplementariesFollowContentInsets = false
                }
                return sectionLayout
            } else {
                // 商品网格section
                let itemSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(0.5), heightDimension: .absolute(270))
                let item = NSCollectionLayoutItem(layoutSize: itemSize)
                item.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 8, bottom: 10, trailing: 8)
                let groupSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1.0), heightDimension: .estimated(270))
                let group = NSCollectionLayoutGroup.horizontal(layoutSize: groupSize, subitems: [item])
                let sectionLayout = NSCollectionLayoutSection(group: group)
                
                sectionLayout.contentInsets = NSDirectionalEdgeInsets(top: 0, leading: 8, bottom: 8, trailing: 8)
                // Header
                let headerSize = NSCollectionLayoutSize(widthDimension: .fractionalWidth(1.0), heightDimension: .absolute(36))
                let header = NSCollectionLayoutBoundarySupplementaryItem(layoutSize: headerSize, elementKind: UICollectionView.elementKindSectionHeader, alignment: .top)
                sectionLayout.boundarySupplementaryItems = [header]
                // 让补充视图（Header/Footer）不受 section contentInsets 的影响，iOS 14+
                if #available(iOS 14.0, *) {
                    sectionLayout.supplementariesFollowContentInsets = false
                }
                return sectionLayout
            }
        }
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .appBackgroundGray
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(VideoHorizontalListCell.self, forCellWithReuseIdentifier: "VideoHorizontalListCell")
        collectionView.register(DiscoverProductCell.self, forCellWithReuseIdentifier: "DiscoverProductCell")
        collectionView.register(SectionHeaderView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: "SectionHeaderView")
        contentView.addSubview(collectionView)
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: contentView.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor)
        ])
    }
    
    private func setupLoadMoreFooter() {
        collectionView.mj_footer = MJRefreshAutoNormalFooter { [weak self] in
            self?.fetchRecommendedProducts(isLoadMore: true)
        }
    }
    
    // MARK: - 数据加载
    private func fetchRecommendedProducts(isLoadMore: Bool) {
        if isLoadingData && isLoadMore { return }
        let pageToFetch: Int
        let size: Int
        if isLoadMore {
            guard currentPage < totalPage - 1 else {
                collectionView.mj_footer?.endRefreshingWithNoMoreData()
                return
            }
            pageToFetch = currentPage + 1
            size = 20 // 加载更多时每页20个
        } else {
            // 首次加载和下拉刷新，重置页码和数据，请求第一页
            pageToFetch = 0
            size = 10 // 首次加载每页10个
            collectionView.mj_footer?.resetNoMoreData()
             // 如果不是加载更多，清空现有数据并回到顶部 (可选，取决于是否支持下拉刷新或重置)
            if !isLoadMore {
                 self.productItems = []
                 self.collectionView.reloadData() // 或者只刷新商品所在的section
            }
        }
        isLoadingData = true
        // TODO: 替换为您的实际商品列表API地址
//        let urlStr = "https://youshu.gzyoushu.com/yigou/api/goods/v1/recomandgood.do?page=\(pageToFetch)&size=\(size)"
        let urlStr = "https://youshu.gzyoushu.com/yigou/api/goods/v1/recomandgood.do?page=\(pageToFetch)&size=\(size)"
        guard let url = URL(string: urlStr) else {
            isLoadingData = false
             if isLoadMore { self.collectionView.mj_footer?.endRefreshing() } else { self.collectionView.mj_header?.endRefreshing() /* 如果有header */ }
            print("RecommendListViewController: 无效的商品列表URL")
            return
        }
        
        print("RecommendListViewController: 开始请求商品列表 - Page \(pageToFetch), Size \(size)")
        
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoadingData = false
                if let error = error {
                    print("RecommendListViewController: 请求商品列表失败 - \(error.localizedDescription)")
                    if isLoadMore { self.collectionView.mj_footer?.endRefreshing() } else { self.collectionView.mj_header?.endRefreshing() /* 如果有header */ }
                    return
                }
                guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                     print("RecommendListViewController: 请求商品列表HTTP状态异常")
                    if isLoadMore { self.collectionView.mj_footer?.endRefreshing() } else { self.collectionView.mj_header?.endRefreshing() /* 如果有header */ }
                    return
                }
                guard let data = data else {
                    print("RecommendListViewController: 请求商品列表返回空数据")
                    if isLoadMore { self.collectionView.mj_footer?.endRefreshing() } else { self.collectionView.mj_header?.endRefreshing() /* 如果有header */ }
                    return
                }
                
                do {
                    let responseModel = try JSONDecoder().decode(RecommendedProductResponse.self, from: data)
                    if responseModel.isSuccess, let pageData = responseModel.data, let newItems = pageData.data {
                        print("RecommendListViewController: 商品列表数据加载成功，新加载\(newItems.count)项")
                        if isLoadMore {
                            self.productItems.append(contentsOf: newItems)
                            let productSectionIndex = self.videoGroups.count
                            self.collectionView.reloadSections(IndexSet(integer: productSectionIndex))
                        } else {
                            // 首次加载或下拉刷新时，为确保布局正确，刷新整个 collectionView
                            self.productItems = newItems
                            self.collectionView.reloadData()
                        }
                        self.currentPage = pageToFetch
                        self.totalPage = pageData.totalPage ?? (self.currentPage + 1)
                        // 根据刷新类型展示日志
                        if isLoadMore {
                            print("RecommendListViewController: 已刷新商品 Section \(self.videoGroups.count) — 加载更多")
                        } else {
                            print("RecommendListViewController: 全量刷新商品数据 — 下拉刷新/首次加载")
                        }
                        if self.currentPage >= self.totalPage - 1 { // 注意这里是currentPage >= totalPage - 1，表示当前页是最后一页（索引从0开始）
                             print("RecommendListViewController: 已加载到最后一页")
                            self.collectionView.mj_footer?.endRefreshingWithNoMoreData()
                        } else {
                             print("RecommendListViewController: 还有更多页，当前页\(self.currentPage)")
                            self.collectionView.mj_footer?.endRefreshing() // 结束加载更多动画
                        }
                    } else {
                        // 使用 errMsg 或一个默认错误消息
                        let errorMessage = responseModel.errMsg ?? "API返回数据结构异常或状态不成功"
                        print("RecommendListViewController: API返回数据异常: \(errorMessage)")
                        if isLoadMore { self.collectionView.mj_footer?.endRefreshing() } else { self.collectionView.mj_header?.endRefreshing() /* 如果有header */ }
                    }
                } catch {
                    print("RecommendListViewController: JSON解析失败 - \(error.localizedDescription)")
                    if isLoadMore { self.collectionView.mj_footer?.endRefreshing() } else { self.collectionView.mj_header?.endRefreshing() /* 如果有header */ }
                }
            }
        }.resume()
    }
    
    // MARK: - UICollectionViewDataSource
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return videoGroups.count + 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if section < videoGroups.count {
            return 1 // 每个分组只1个横滑cell
        } else {
            return productItems.count // 商品数量取决于获取到的数据
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.section < videoGroups.count {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "VideoHorizontalListCell", for: indexPath) as! VideoHorizontalListCell
            let group = videoGroups[indexPath.section]
            cell.delegate = self
            cell.configure(with: group.videos, group: group)
            return cell
        } else {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DiscoverProductCell", for: indexPath) as! DiscoverProductCell
            let item = productItems[indexPath.item]
            cell.configureWithRecommendedItem(item)
            return cell
        }
    }
    
    // Section Header
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        let header = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: "SectionHeaderView", for: indexPath) as! SectionHeaderView
        if indexPath.section < videoGroups.count {
            header.titleLabel.text = videoGroups[indexPath.section].title
        } else {
            header.titleLabel.text = "热销商品"
        }
        return header
    }
    
    // MARK: - UICollectionViewDelegate
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.section < videoGroups.count {
            // 视频cell的点击事件现在由VideoHorizontalListCell通过代理处理
            // 这里不需要额外处理
            
        } else {
            let product = productItems[indexPath.item]
            // 通过代理回调处理商品点击
            delegate?.recommendListViewController(self, didSelectProduct: product)
        }
    }

    // MARK: - 对外暴露：滚动到顶部
    func scrollToTop() {
        // 1. 在下一轮 RunLoop 进行带动画的滚动，以确保 reloadData 完成后有平滑过渡
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            let topOffset = CGPoint(x: 0, y: -self.collectionView.adjustedContentInset.top)
            self.collectionView.setContentOffset(topOffset, animated: true)
        }
  
        // 2. 同步横向列表动画复位
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            for cell in self.collectionView.visibleCells {
                if let videoCell = cell as? VideoHorizontalListCell {
                    // 使用动画与竖向滚动保持一致
                    videoCell.scrollToFirst(animated: true)
                }
            }
        }
    }

    @objc private func handlePullToRefresh() {
        reloadData { [weak self] in
            self?.collectionView.mj_header?.endRefreshing()
        }
    }

    // 添加网络加载视频分组的方法并在viewDidLoad中调用
    private func loadVideoGroups() {
        // 请求首页推荐作品数据
        APIManager.shared.getMainWorksInfo(worksCategoryId: nil) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    guard response.isSuccess, let groups = response.data else {
                        print("RecommendListViewController: 获取首页推荐视频失败 - \(response.displayMessage)")
                        self.videoGroups = []
                        self.collectionView.reloadData()
                        return
                    }
                    // 将接口数据直接转换为内部数据模型 (使用 VideoItem)
                    let mappedGroups: [VideoGroup] = groups.compactMap { group in
                        let title = group.groupName
                        let isLive = (group.type?.typeName == "直播")
                        let videos: [VideoItem] = group.list ?? []
                        guard !videos.isEmpty else { return nil }
                        return VideoGroup(title: title, videos: videos, isLive: isLive)
                    }
                    self.videoGroups = mappedGroups
                    // 刷新视频 section
                    self.collectionView.reloadData()
                case .failure(let error):
                    print("RecommendListViewController: 请求首页推荐视频接口失败 - \(error.localizedDescription)")
                }
            }
        }
    }
}

// MARK: - VideoHorizontalListCellDelegate
extension RecommendListViewController: VideoHorizontalListCellDelegate {
    func videoHorizontalListCell(_ cell: VideoHorizontalListCell, didSelectVideo video: VideoItem, inGroup group: VideoGroup) {
        delegate?.recommendListViewController(self, didSelectVideo: video, inGroup: group)
    }
}

// MARK: - JXSegmentedListContainerViewListDelegate 实现
extension RecommendListViewController: JXSegmentedListContainerViewListDelegate {
    func listView() -> UIView {
        return view
    }

    func listDidAppear() {
        print("RecommendListViewController: listDidAppear - 推荐页显示，开始加载数据")
        // 当推荐页显示时，确保数据已加载
        if videoGroups.isEmpty {
            loadVideoGroups()
        }
        if productItems.isEmpty {
            fetchRecommendedProducts(isLoadMore: false)
        }
    }

    func listWillAppear() {
        print("RecommendListViewController: listWillAppear - 推荐页即将显示")
    }

    func listDidDisappear() {
        print("RecommendListViewController: listDidDisappear - 推荐页已隐藏")
    }

    func listWillDisappear() {
        print("RecommendListViewController: listWillDisappear - 推荐页即将隐藏")
    }
}

// MARK: - ChannelRefreshable 实现
extension RecommendListViewController: ChannelRefreshable {
    func reloadData(completion: @escaping () -> Void) {
        // 重新加载商品和视频数据
        fetchRecommendedProducts(isLoadMore: false)
        loadVideoGroups()
        // 简单延迟作为占位，实际应在数据加载完成后调用
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            completion()
        }
    }
}
