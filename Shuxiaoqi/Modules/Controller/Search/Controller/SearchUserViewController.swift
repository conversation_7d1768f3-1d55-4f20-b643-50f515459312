import UIKit
import SnapKit

class SearchUserViewController: UIViewController, Searchable {
    // MARK: - 数据模型
    private var users: [SearchUser] = []
    private var isOnlyOnePage: Bool = false // 是否只有一页数据
    private var currentUserId: String = "" // 当前登录用户的ID

    // MARK: - UI
    private lazy var tableView: UITableView = {
        let tv = UITableView(frame: .zero, style: .plain)
        tv.backgroundColor = UIColor(hex: "#F5F5F5")
        tv.separatorStyle = .none
        tv.delegate = self
        tv.dataSource = self
        tv.register(SearchUserTableViewCell.self, forCellReuseIdentifier: SearchUserTableViewCell.reuseIdentifier)
        return tv
    }()

    /// 空数据占位视图
    private lazy var emptyStateView: UIView = {
        let container = UIView()
        let imageView = UIImageView(image: UIImage(named: "empty_data_placeholder_image"))
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(imageView)

        let label = UILabel()
        label.text = "暂无相关用户"
        label.textColor = .lightGray
        label.font = .systemFont(ofSize: 14)
        label.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(label)

        NSLayoutConstraint.activate([
            imageView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
            imageView.centerYAnchor.constraint(equalTo: container.centerYAnchor, constant: -20),
            imageView.widthAnchor.constraint(equalToConstant: 120),
            imageView.heightAnchor.constraint(equalToConstant: 120),

            label.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 12),
            label.centerXAnchor.constraint(equalTo: container.centerXAnchor)
        ])
        return container
    }()

    /// 没有更多数据的脚标视图
    private lazy var noMoreDataFooterView: UIView = {
        let container = UIView()
        container.frame = CGRect(x: 0, y: 0, width: 0, height: 50)

        let label = UILabel()
        label.text = "没有更多相关用户"
        label.textColor = UIColor(hex: "#999999")
        label.font = .systemFont(ofSize: 14)
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(label)

        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: container.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: container.centerYAnchor)
        ])

        return container
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.addSubview(tableView)
        tableView.snp.makeConstraints { $0.edges.equalTo(view.safeAreaLayoutGuide) }

        updateEmptyState() // 初始显示空态
        updateTableFooter() // 初始设置脚标
        fetchCurrentUserInfo() // 获取当前用户信息
    }

    private func requestUsers(keyword: String) {
        APIManager.shared.searchUser(keywords: keyword, page: 0, size: 20) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    self.users = response.data.list.map { item in
                        // 优先使用服务器返回的isSelf字段，如果为false则用客户端比较作为备用
                        let isSelf = item.isSelf || (item.customerId == self.currentUserId)
                        return SearchUser(
                            avatar: item.wxAvator,
                            name: item.displayNickName,
                            subtitle: "", // 隐藏中间内容
                            followers: item.fansNum,
                            treeId: item.customerId,
                            customerAccount: item.customerAccount,
                            relationTag: nil,
                            isLive: false,
                            isFollowing: item.isFollow,
                            isSelf: isSelf)
                    }

                    // 判断是否只有一页数据（当前请求的是第0页，size是20）
                    self.isOnlyOnePage = response.data.list.count < 20

                    self.tableView.reloadData()
                    self.updateEmptyState()
                    self.updateTableFooter()
                case .failure(let error):
                    print("搜索用户 API 错误：\(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - Searchable
    func search(with keyword: String) {
        print("[User] 搜索关键词：\(keyword)")
        requestUsers(keyword: keyword)
    }

    // MARK: - 空数据视图处理
    private func updateEmptyState() {
        if users.isEmpty {
            tableView.backgroundView = emptyStateView
        } else {
            tableView.backgroundView = nil
        }
    }

    // MARK: - 表格脚标处理
    private func updateTableFooter() {
        if !users.isEmpty && isOnlyOnePage {
            tableView.tableFooterView = noMoreDataFooterView
        } else {
            tableView.tableFooterView = UIView() // 空视图，隐藏多余的分割线
        }
    }

    // MARK: - 获取当前用户信息
    private func fetchCurrentUserInfo() {
        APIManager.shared.getUserInfo { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if let userData = response.data {
                        self.currentUserId = userData.customerId
                        print("当前用户ID: \(self.currentUserId)")
                    }
                case .failure(let error):
                    print("获取当前用户信息失败: \(error.localizedDescription)")
                }
            }
        }
    }
}

extension SearchUserViewController: UITableViewDataSource, UITableViewDelegate {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int { users.count }
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: SearchUserTableViewCell.reuseIdentifier, for: indexPath) as! SearchUserTableViewCell
        cell.configure(with: users[indexPath.row])

        // 设置关注状态变化回调
        cell.onFollowStatusChanged = { [weak self] userId, isFollowing in
            guard let self = self else { return }
            // 更新本地数据
            if let index = self.users.firstIndex(where: { $0.treeId == userId }) {
                self.users[index].isFollowing = isFollowing
            }
        }

        // 最后一个 Cell 不显示分割线
        let isLastCell = indexPath.row == users.count - 1
        cell.setSeparatorHidden(isLastCell)

        return cell
    }
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat { 73 } // 原95减去22pt

    // 用户点击搜索结果单元行，跳转至个人主页或创作中心
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let selectedUser = users[indexPath.row]
        // 确保拥有有效的用户 ID
        guard !selectedUser.treeId.isEmpty else { return }

        if selectedUser.isSelf {
            // 如果是自己，跳转到创作中心
            let creativeCenterVC = CreativeCenterViewController()
            navigationController?.pushViewController(creativeCenterVC, animated: true)
        } else {
            // 如果不是自己，跳转到个人主页
            let personalVC = PersonalHomepageViewController()
            personalVC.userId = selectedUser.treeId
            navigationController?.pushViewController(personalVC, animated: true)
        }
    }
}

// 数据模型
struct SearchUser {
    let avatar: String
    let name: String
    let subtitle: String
    let followers: Int
    let treeId: String
    let customerAccount: String
    let relationTag: String?
    let isLive: Bool
    var isFollowing: Bool
    let isSelf: Bool // 是否为当前用户自己
}

private extension UIColor {
    convenience init(hex: String) {
        let hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines).replacingOccurrences(of: "#", with: "")
        var rgb: UInt64 = 0
        Scanner(string: hexSanitized).scanHexInt64(&rgb)
        let r = CGFloat((rgb & 0xFF0000) >> 16)/255
        let g = CGFloat((rgb & 0x00FF00) >> 8)/255
        let b = CGFloat(rgb & 0x0000FF)/255
        self.init(red: r, green: g, blue: b, alpha: 1)
    }
} 
